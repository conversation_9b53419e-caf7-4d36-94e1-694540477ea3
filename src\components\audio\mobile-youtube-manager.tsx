'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { useGlobalMusicControl } from '@/lib/use-global-music-control';
import { YouTubePlayerState } from '@/app/timer/_components/musics-control/types';

declare global {
  interface Window {
    YT: any;
    onYouTubeIframeAPIReady: () => void;
    mobileYouTubeManager?: MobileYouTubeManagerInstance;
  }
}

interface MobileYouTubeManagerInstance {
  playerState: YouTubePlayerState;
  togglePlay: () => void;
  handleVolumeChange: (volume: number) => void;
  toggleMute: () => void;
  handleUrlChange: (url: string) => void;
  loadVideo: () => void;
  pauseVideo: () => void;
  isPlayerReady: boolean;
  subscribe: (callback: (state: YouTubePlayerState) => void) => () => void;
}

/**
 * Mobile YouTube Manager - Maintains YouTube player instance independently of UI
 * This component is always mounted on mobile to ensure YouTube audio continues
 * playing even when the music control sheet is closed
 */
export function MobileYouTubeManager() {
  const [playerState, setPlayerState] = useState<YouTubePlayerState>({
    currentVideoId: '',
    isPlaying: false,
    volume: 70,
    isMuted: false,
    inputUrl: 'https://www.youtube.com/watch?v=hlWiI4xVXKY'
  });

  const playerRef = useRef<any>(null);
  const isAPILoadedRef = useRef(false);
  const isPlayerReadyRef = useRef(false);
  const subscribersRef = useRef<Set<(state: YouTubePlayerState) => void>>(new Set());

  // Tab visibility handling
  const wasPlayingBeforeHiddenRef = useRef(false);
  const visibilityRetryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const visibilityRetryCountRef = useRef(0);

  // Global music control integration
  const globalMusicControl = useGlobalMusicControl({
    sourceId: 'mobile-youtube-player',
    sourceType: 'youtube',
    sourceName: 'Mobile YouTube Player',
    persistOnUnmount: true,
    onPlayStart: () => {
      console.log('Mobile YouTube Manager: Play start requested by global control');
    },
    onPause: () => {
      if (playerRef.current && isPlayerReadyRef.current && typeof playerRef.current.pauseVideo === 'function') {
        try {
          playerRef.current.pauseVideo();
        } catch (error) {
          console.error('Mobile YouTube Manager: Error pausing video from global control:', error);
        }
      }
    },
  });

  // Notify subscribers when state changes
  const notifySubscribers = useCallback((newState: YouTubePlayerState) => {
    subscribersRef.current.forEach(callback => {
      try {
        callback(newState);
      } catch (error) {
        console.error('Mobile YouTube Manager: Error notifying subscriber:', error);
      }
    });
  }, []);

  // Update state and notify subscribers
  const updatePlayerState = useCallback((updates: Partial<YouTubePlayerState>) => {
    setPlayerState(prev => {
      const newState = { ...prev, ...updates };
      // Use setTimeout to ensure state is updated before notifying
      setTimeout(() => notifySubscribers(newState), 0);
      return newState;
    });
  }, [notifySubscribers]);

  // Extract video ID from YouTube URL
  const extractVideoId = useCallback((url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /^([a-zA-Z0-9_-]{11})$/ // Direct video ID
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  }, []);

  // Create YouTube player
  const createPlayer = useCallback(() => {
    if (!window.YT || !window.YT.Player || !playerState.currentVideoId) return;

    try {
      // Destroy existing player if it exists
      if (playerRef.current && typeof playerRef.current.destroy === 'function') {
        playerRef.current.destroy();
      }

      playerRef.current = new window.YT.Player('mobile-youtube-player', {
        height: '100%',
        width: '100%',
        videoId: playerState.currentVideoId,
        playerVars: {
          autoplay: 0,
          controls: 1,
          modestbranding: 1,
          rel: 0,
          showinfo: 0,
          iv_load_policy: 3,
          disablekb: 0,
          fs: 1,
          cc_load_policy: 0,
          origin: window.location.origin,
          enablejsapi: 1,
          // Optimizations for background playback
          playsinline: 1, // Helps with mobile background playback
          widget_referrer: window.location.origin,
        },
        events: {
          onReady: (event: any) => {
            isPlayerReadyRef.current = true;
            event.target.setVolume(playerState.volume);
            if (playerState.isMuted) {
              event.target.mute();
            }
            console.log('Mobile YouTube Manager: Player ready');
          },
          onStateChange: (event: any) => {
            const isCurrentlyPlaying = event.data === window.YT.PlayerState.PLAYING;
            const wasPlaying = playerState.isPlaying;

            updatePlayerState({ isPlaying: isCurrentlyPlaying });

            // Update global music control state
            if (isCurrentlyPlaying && !wasPlaying) {
              const videoTitle = playerRef.current?.getVideoData?.()?.title || 'YouTube Video';
              globalMusicControl.notifyPlayStart({
                id: playerState.currentVideoId,
                title: videoTitle,
                src: `https://www.youtube.com/watch?v=${playerState.currentVideoId}`,
                type: 'music',
              });
            } else if (!isCurrentlyPlaying && wasPlaying) {
              globalMusicControl.notifyPause();
            }

            globalMusicControl.updateState({
              isPlaying: isCurrentlyPlaying,
              volume: playerState.volume,
              isMuted: playerState.isMuted,
            });
          },
          onError: (event: any) => {
            console.error('Mobile YouTube Manager: Player error:', event.data);
            isPlayerReadyRef.current = false;
          }
        },
      });
    } catch (error) {
      console.error('Mobile YouTube Manager: Error creating player:', error);
      isPlayerReadyRef.current = false;
    }
  }, [playerState.currentVideoId, playerState.volume, playerState.isMuted, playerState.isPlaying, updatePlayerState, globalMusicControl]);

  // Handle tab visibility changes to maintain background playback
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!playerRef.current || !isPlayerReadyRef.current) return;

      if (document.hidden) {
        // Tab became hidden - store current playing state
        try {
          const currentState = playerRef.current.getPlayerState();
          wasPlayingBeforeHiddenRef.current = currentState === window.YT?.PlayerState?.PLAYING;
          console.log('Mobile YouTube Manager: Tab hidden, was playing:', wasPlayingBeforeHiddenRef.current);
        } catch (error) {
          console.error('Mobile YouTube Manager: Error checking player state on hide:', error);
        }
      } else {
        // Tab became visible - attempt to resume playback if it was playing before
        if (wasPlayingBeforeHiddenRef.current) {
          console.log('Mobile YouTube Manager: Tab visible, attempting to resume playback');

          // Clear any existing retry timeout
          if (visibilityRetryTimeoutRef.current) {
            clearTimeout(visibilityRetryTimeoutRef.current);
          }

          // Reset retry count
          visibilityRetryCountRef.current = 0;

          // Attempt to resume playback with retries
          const attemptResume = () => {
            try {
              if (playerRef.current && isPlayerReadyRef.current) {
                const currentState = playerRef.current.getPlayerState();

                // Only try to resume if not already playing
                if (currentState !== window.YT?.PlayerState?.PLAYING) {
                  playerRef.current.playVideo();
                  console.log('Mobile YouTube Manager: Resume attempt', visibilityRetryCountRef.current + 1);

                  // Verify playback started after a short delay
                  setTimeout(() => {
                    if (playerRef.current && isPlayerReadyRef.current) {
                      const newState = playerRef.current.getPlayerState();
                      if (newState !== window.YT?.PlayerState?.PLAYING && visibilityRetryCountRef.current < 3) {
                        visibilityRetryCountRef.current++;
                        visibilityRetryTimeoutRef.current = setTimeout(attemptResume, 1000);
                      } else if (newState === window.YT?.PlayerState?.PLAYING) {
                        console.log('Mobile YouTube Manager: Successfully resumed playback');
                        wasPlayingBeforeHiddenRef.current = false; // Reset flag
                      }
                    }
                  }, 500);
                } else {
                  console.log('Mobile YouTube Manager: Already playing, no resume needed');
                  wasPlayingBeforeHiddenRef.current = false; // Reset flag
                }
              }
            } catch (error) {
              console.error('Mobile YouTube Manager: Error attempting to resume playback:', error);
            }
          };

          // Start resume attempt after a short delay to let the tab fully activate
          visibilityRetryTimeoutRef.current = setTimeout(attemptResume, 100);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (visibilityRetryTimeoutRef.current) {
        clearTimeout(visibilityRetryTimeoutRef.current);
      }
    };
  }, []);

  // Load YouTube API
  useEffect(() => {
    if (isAPILoadedRef.current) return;

    const loadYouTubeAPI = () => {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag);
    };

    if (!window.YT) {
      loadYouTubeAPI();
    }

    window.onYouTubeIframeAPIReady = () => {
      isAPILoadedRef.current = true;
      console.log('Mobile YouTube Manager: API loaded');
    };

    if (window.YT && window.YT.Player) {
      isAPILoadedRef.current = true;
    }

    return () => {
      if (playerRef.current && typeof playerRef.current.destroy === 'function') {
        try {
          playerRef.current.destroy();
        } catch (error) {
          console.error('Mobile YouTube Manager: Error destroying player:', error);
        }
      }
      isPlayerReadyRef.current = false;

      // Clean up visibility handling
      if (visibilityRetryTimeoutRef.current) {
        clearTimeout(visibilityRetryTimeoutRef.current);
      }
    };
  }, []);

  // Create player when video ID changes
  useEffect(() => {
    if (playerState.currentVideoId && isAPILoadedRef.current && !playerRef.current) {
      createPlayer();
    }
  }, [playerState.currentVideoId, createPlayer]);

  // Manager API functions
  const togglePlay = useCallback(() => {
    if (!playerRef.current || !isPlayerReadyRef.current) return;

    try {
      if (playerState.isPlaying) {
        playerRef.current.pauseVideo();
        globalMusicControl.notifyPause();
      } else {
        const canPlay = globalMusicControl.requestControl();
        if (canPlay) {
          playerRef.current.playVideo();
        }
      }
    } catch (error) {
      console.error('Mobile YouTube Manager: Error toggling play:', error);
    }
  }, [playerState.isPlaying, globalMusicControl]);

  const handleVolumeChange = useCallback((volume: number) => {
    updatePlayerState({ volume, isMuted: false });
    if (playerRef.current && isPlayerReadyRef.current) {
      try {
        playerRef.current.setVolume(volume);
        if (volume > 0) {
          playerRef.current.unMute();
        }
      } catch (error) {
        console.error('Mobile YouTube Manager: Error changing volume:', error);
      }
    }
    globalMusicControl.updateState({ volume, isMuted: false });
  }, [updatePlayerState, globalMusicControl]);

  const toggleMute = useCallback(() => {
    const newMutedState = !playerState.isMuted;
    updatePlayerState({ isMuted: newMutedState });

    if (playerRef.current && isPlayerReadyRef.current) {
      try {
        if (playerState.isMuted) {
          playerRef.current.unMute();
        } else {
          playerRef.current.mute();
        }
      } catch (error) {
        console.error('Mobile YouTube Manager: Error toggling mute:', error);
      }
    }
    globalMusicControl.updateState({ isMuted: newMutedState });
  }, [playerState.isMuted, updatePlayerState, globalMusicControl]);

  const handleUrlChange = useCallback((url: string) => {
    updatePlayerState({ inputUrl: url });
  }, [updatePlayerState]);

  const loadVideo = useCallback(() => {
    const videoId = extractVideoId(playerState.inputUrl);
    if (!videoId) {
      alert('Please enter a valid YouTube URL');
      return;
    }

    updatePlayerState({ currentVideoId: videoId });
    
    if (playerRef.current && isPlayerReadyRef.current && typeof playerRef.current.loadVideoById === 'function') {
      try {
        if (typeof playerRef.current.stopVideo === 'function') {
          playerRef.current.stopVideo();
        }
        
        playerRef.current.loadVideoById({
          videoId: videoId,
          startSeconds: 0
        });
        
        setTimeout(() => {
          if (playerRef.current && isPlayerReadyRef.current) {
            playerRef.current.setVolume(playerState.volume);
            if (playerState.isMuted) {
              playerRef.current.mute();
            }
            
            if (typeof playerRef.current.playVideo === 'function') {
              const canPlay = globalMusicControl.requestControl();
              if (canPlay) {
                playerRef.current.playVideo();
              }
            }
          }
        }, 500);
      } catch (error) {
        console.error('Mobile YouTube Manager: Error loading video:', error);
        createPlayer();
      }
    } else {
      if (isAPILoadedRef.current) {
        createPlayer();
      }
    }
  }, [playerState.inputUrl, playerState.volume, playerState.isMuted, extractVideoId, createPlayer, updatePlayerState, globalMusicControl]);

  const pauseVideo = useCallback(() => {
    if (playerRef.current && isPlayerReadyRef.current && typeof playerRef.current.pauseVideo === 'function') {
      try {
        playerRef.current.pauseVideo();
        globalMusicControl.notifyPause();
      } catch (error) {
        console.error('Mobile YouTube Manager: Error pausing video:', error);
      }
    }
  }, [globalMusicControl]);

  const subscribe = useCallback((callback: (state: YouTubePlayerState) => void) => {
    subscribersRef.current.add(callback);
    // Immediately call with current state
    callback(playerState);
    
    return () => {
      subscribersRef.current.delete(callback);
    };
  }, [playerState]);

  // Expose manager instance globally
  useEffect(() => {
    const managerInstance: MobileYouTubeManagerInstance = {
      playerState,
      togglePlay,
      handleVolumeChange,
      toggleMute,
      handleUrlChange,
      loadVideo,
      pauseVideo,
      isPlayerReady: isPlayerReadyRef.current,
      subscribe,
    };

    window.mobileYouTubeManager = managerInstance;
    console.log('Mobile YouTube Manager: Instance exposed globally');

    return () => {
      delete window.mobileYouTubeManager;
    };
  }, [playerState, togglePlay, handleVolumeChange, toggleMute, handleUrlChange, loadVideo, pauseVideo, subscribe]);

  // Render hidden YouTube player container
  return (
    <div 
      id="mobile-youtube-player" 
      style={{
        position: 'fixed',
        top: '-9999px',
        left: '-9999px',
        width: '320px',
        height: '180px',
        visibility: 'hidden',
        pointerEvents: 'none'
      }}
    />
  );
}
